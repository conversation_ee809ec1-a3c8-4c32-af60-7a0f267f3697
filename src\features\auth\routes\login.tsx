import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { z } from "zod";

// Validation schema using Zod
const loginSchema = z.object({
  email: z.string().email("Please enter a valid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

type LoginFormInputs = z.infer<typeof loginSchema>;

const AGKraftLogin = () => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormInputs>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormInputs) => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    console.log("Login data:", data);
  };

  return (
    <div className="min-h-screen flex bg-gradient-to-br from-slate-900 via-gray-900 to-black">
      {/* Background overlay for better gradient effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-pink-900/20"></div>

      {/* Main container with proper centering and padding */}
      <div className="relative z-10 flex w-full min-h-screen p-4 sm:p-6 lg:p-8">
        {/* Left sidebar */}
        <aside className="hidden lg:flex w-2/5 items-center justify-center bg-gradient-to-br from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl mr-8 shadow-2xl border border-gray-700/50">
          <div className="text-center px-8 py-12">
            <div className="w-48 h-48 mx-auto rounded-2xl bg-gradient-to-br from-[#F44336] to-[#FF9800] flex items-center justify-center shadow-2xl transform hover:scale-105 transition-transform duration-300">
              <img
                src="/assets/agkraft-logo-white.png"
                alt="AGKRAFT"
                className="w-28 h-28 object-contain"
              />
            </div>
            <h2 className="mt-8 text-white text-3xl font-bold tracking-tight">
              AGKRAFT Admin
            </h2>
            <p className="text-gray-300 mt-4 text-lg leading-relaxed">
              Secure admin panel — sign in to manage clients & invoices
            </p>
            <div className="mt-8 flex justify-center space-x-2">
              <div className="w-2 h-2 bg-[#F44336] rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-[#FF9800] rounded-full animate-pulse delay-100"></div>
              <div className="w-2 h-2 bg-[#F44336] rounded-full animate-pulse delay-200"></div>
            </div>
          </div>
        </aside>

        {/* Login card container */}
        <main className="flex-1 flex items-center justify-center lg:w-3/5 max-w-lg mx-auto lg:mx-0">
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl shadow-2xl p-8 sm:p-12 border border-gray-200/50">
            {/* Top logo for small screens */}
            <div className="flex items-center gap-4 mb-8 lg:hidden">
              <div className="w-14 h-14 rounded-xl bg-gradient-to-br from-[#F44336] to-[#FF9800] flex items-center justify-center shadow-lg">
                <img
                  src="/assets/agkraft-logo-dark.png"
                  alt="AGKRAFT"
                  className="w-9 h-9 object-contain"
                />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  Welcome back
                </h1>
                <p className="text-sm text-gray-600">Admin sign in</p>
              </div>
            </div>

            <div className="hidden lg:block mb-8">
              <h1 className="text-4xl font-bold text-gray-900 mb-2">
                Sign in to AGKRAFT
              </h1>
              <p className="text-gray-600">
                Use your admin credentials to access the dashboard
              </p>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Email */}
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-semibold text-gray-800 mb-2"
                >
                  Email address
                </label>
                <input
                  {...register("email")}
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  className={`block w-full rounded-2xl border-2 bg-gray-50/50 px-5 py-4 text-sm shadow-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#F44336] focus:border-transparent transition-all duration-200 ${
                    errors.email ? "border-red-500 bg-red-50/50" : "border-gray-200 hover:border-gray-300"
                  }`}
                />
                {errors.email && (
                  <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Password */}
              <div>
                <div className="flex items-center justify-between mb-2">
                  <label
                    htmlFor="password"
                    className="block text-sm font-semibold text-gray-800"
                  >
                    Password
                  </label>
                  <a href="#" className="text-sm text-[#F44336] hover:text-[#FF9800] transition-colors duration-200 font-medium">
                    Forgot?
                  </a>
                </div>
                <input
                  {...register("password")}
                  id="password"
                  type="password"
                  placeholder="Enter your password"
                  className={`block w-full rounded-2xl border-2 bg-gray-50/50 px-5 py-4 text-sm shadow-sm placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-[#F44336] focus:border-transparent transition-all duration-200 ${
                    errors.password ? "border-red-500 bg-red-50/50" : "border-gray-200 hover:border-gray-300"
                  }`}
                />
                {errors.password && (
                  <p className="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                    {errors.password.message}
                  </p>
                )}
              </div>

              {/* Remember me */}
              <div className="flex items-center justify-between">
                <label className="inline-flex items-center gap-3 text-sm cursor-pointer">
                  <input
                    type="checkbox"
                    className="h-5 w-5 rounded-lg border-2 border-gray-300 text-[#F44336] focus:ring-[#F44336] focus:ring-2 transition-all duration-200"
                  />
                  <span className="text-gray-700 font-medium">Remember me</span>
                </label>
                <a href="#" className="text-sm text-gray-600 hover:text-[#F44336] transition-colors duration-200 font-medium">
                  Need help?
                </a>
              </div>

              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full rounded-2xl px-6 py-4 text-white font-bold text-lg shadow-lg bg-gradient-to-r from-[#F44336] to-[#FF9800] hover:from-[#E53935] hover:to-[#FB8C00] hover:scale-[1.02] transform transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed disabled:transform-none"
              >
                {isSubmitting ? (
                  <div className="flex items-center justify-center gap-2">
                    <svg className="animate-spin h-5 w-5" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </div>
                ) : (
                  "Sign in"
                )}
              </button>
            </form>

            <div className="mt-8 text-center text-sm text-gray-500">
              By signing in you agree to AGKRAFT's{" "}
              <a className="text-[#F44336] hover:text-[#FF9800] transition-colors duration-200 font-medium" href="#">
                terms
              </a>
              .
            </div>
          </div>

          <div className="mt-6 text-center text-sm text-gray-400">
            <span>Need a sandbox account? </span>
            <a href="#" className="text-[#F44336] hover:text-[#FF9800] transition-colors duration-200 font-semibold">
              Contact support
            </a>
          </div>
        </main>
      </div>
    </div>
  );
};

export default AGKraftLogin;
